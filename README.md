# GuideOCR - PDF OCR Processing with Google Gemini AI

A modern TypeScript application that processes PDF files using OCR technology powered by Google Gemini AI. Built with pnpm and tsx for fast development without compilation. Specifically designed to extract structured business directory information and export it to Excel format.

## Features

- **PDF to Image Conversion**: Converts PDF pages to high-quality images for OCR processing
- **Google Gemini AI Integration**: Uses Gemini Vision API for intelligent text extraction
- **Structured Data Extraction**: Extracts business information including:
  - Business names and categories
  - Contact information (emails, phones, websites)
  - Addresses and social media
  - Brand names and additional comments
- **Excel Export**: Generates well-organized Excel files with formatting and metadata
- **Trial Mode**: Process 1-3 pages for testing and validation
- **Batch Processing**: Handle large PDFs in manageable chunks (10-25 pages)
- **Error Handling**: Robust error handling with detailed logging

## Prerequisites

- Node.js 18.0.0 or higher
- pnpm (package manager)
- tsx (globally installed TypeScript executor)
- Google Gemini API key
- PDF files to process

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd GuideOCR
```

2. Install dependencies with pnpm:
```bash
pnpm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

4. Edit `.env` file and add your Google Gemini API key:
```env
GEMINI_API_KEY=your_gemini_api_key_here
```

## Usage

### No Build Required!
This project uses tsx for direct TypeScript execution - no compilation step needed.

### Trial Mode (Recommended for first use):
```bash
pnpm trial
# or with custom PDF
pnpm trial ./path/to/your/file.pdf
```

### Process a full PDF:
```bash
pnpm dev process ./pdf/your-file.pdf
```

### Command Line Options:
```bash
# Process with custom options
pnpm dev process ./pdf/file.pdf --trial --start-page 1 --end-page 5 --batch-size 10

# Available options:
# -t, --trial          Run in trial mode (1-3 pages only)
# -s, --start-page     Start page number (default: 1)
# -e, --end-page       End page number (-1 for all, default: -1)
# -b, --batch-size     Batch size for processing (default: 25)
```

### Development Commands:
```bash
pnpm dev          # Run the application
pnpm trial        # Run in trial mode
pnpm lint         # Check code quality
pnpm lint:fix     # Fix linting issues
pnpm clean        # Clean temporary files
pnpm setup        # Check setup status
```

## Configuration

The application can be configured through environment variables in the `.env` file:

### Required Configuration:
- `GEMINI_API_KEY`: Your Google Gemini API key

### Optional Configuration:
- `TRIAL_MODE`: Enable trial mode by default (default: false)
- `MAX_PAGES_PER_BATCH`: Maximum pages per batch (default: 25)
- `TRIAL_MAX_PAGES`: Maximum pages in trial mode (default: 3)
- `OUTPUT_DIR`: Output directory for Excel files (default: ./output)
- `TEMP_DIR`: Temporary directory for image files (default: ./temp)
- `IMAGE_FORMAT`: Image format for conversion (png, tiff, jpeg - default: png)
- `IMAGE_QUALITY`: Image quality 1-100 (default: 95)
- `IMAGE_DPI`: Image DPI for conversion (default: 300)

## Output

The application generates:

1. **Excel File**: Contains extracted data with columns:
   - Business Name
   - Category
   - Contact Person
   - Email
   - Phone
   - Website
   - Address
   - Social Media
   - Brand Names
   - Comments
   - Page Number
   - Confidence %

2. **Metadata Sheet**: Processing statistics and configuration details

3. **Logs**: Detailed processing logs in `./logs/app.log`

## Project Structure

```
src/
├── config/
│   └── config.ts          # Configuration management
├── services/
│   ├── pdfProcessor.ts    # PDF to image conversion
│   ├── geminiService.ts   # Google Gemini AI integration
│   └── excelExporter.ts   # Excel file generation
├── types/
│   └── types.ts           # TypeScript type definitions
├── utils/
│   ├── logger.ts          # Logging utilities
│   └── fileUtils.ts       # File handling utilities
└── index.ts               # Main application entry point
```

## API Integration

### Google Gemini API

The application uses the Google Gemini 2.0 Flash model for document understanding. Key features:

- **Native PDF Vision**: Processes images with text and visual elements
- **Structured Output**: Extracts data in JSON format
- **High Accuracy**: Handles various document layouts and formats
- **Batch Processing**: Processes multiple pages efficiently

### Getting a Gemini API Key

1. Visit [Google AI Studio](https://aistudio.google.com/)
2. Sign in with your Google account
3. Create a new API key
4. Copy the key to your `.env` file

## Error Handling

The application includes comprehensive error handling:

- **PDF Validation**: Checks file existence, format, and size
- **Image Conversion**: Handles conversion failures gracefully
- **API Errors**: Retries and fallback mechanisms
- **Data Validation**: Validates extracted data structure
- **Cleanup**: Automatic cleanup of temporary files

## Logging

Detailed logging is provided at multiple levels:

- **Console Output**: Real-time progress and results
- **File Logging**: Detailed logs saved to `./logs/app.log`
- **Error Tracking**: Comprehensive error logging and stack traces

## Performance Considerations

- **Memory Usage**: Large PDFs are processed in batches to manage memory
- **API Limits**: Respects Google Gemini API rate limits
- **Image Quality**: Configurable DPI and quality settings for optimal OCR
- **Cleanup**: Automatic cleanup of temporary files to save disk space
- **Development Speed**: No compilation step required - instant execution with tsx
- **Package Management**: pnpm provides faster installs and better disk usage

## Troubleshooting

### Common Issues:

1. **API Key Error**: Ensure your Gemini API key is valid and has sufficient quota
2. **PDF Conversion Failed**: Check if the PDF is corrupted or password-protected
3. **No Data Extracted**: Try adjusting image quality settings or check PDF content
4. **Memory Issues**: Reduce batch size for very large PDFs

### Debug Mode:

Set `LOG_LEVEL=debug` in your `.env` file for detailed debugging information.

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs for detailed error information
3. Create an issue in the repository with relevant details
