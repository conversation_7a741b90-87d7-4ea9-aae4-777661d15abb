{"name": "guide-ocr", "version": "1.0.0", "description": "TypeScript application for OCR processing of PDF files with Google Gemini AI", "type": "module", "main": "src/index.ts", "scripts": {"start": "tsx src/index.ts", "dev": "tsx src/index.ts", "trial": "tsx src/index.ts trial", "process": "tsx src/index.ts process", "clean": "rm -rf temp output/*.xlsx logs/*.log", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "test": "tsx --test src/**/*.test.ts", "setup": "node setup-check.js"}, "keywords": ["ocr", "pdf", "gemini", "typescript", "excel"], "author": "", "license": "MIT", "packageManager": "pnpm@9.0.0", "dependencies": {"@google/generative-ai": "^0.24.1", "commander": "^14.0.0", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "pdf2pic": "^3.2.0", "sharp": "^0.34.2", "winston": "^3.17.0"}, "devDependencies": {"@types/node": "^24.0.0", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "prettier": "^3.5.3", "typescript": "^5.8.3", "typescript-eslint": "^8.34.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}