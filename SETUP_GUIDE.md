# GuideOCR Setup Guide (pnpm + tsx)

## 🎯 Quick Start

Your modern TypeScript OCR application is ready! Built with pnpm and tsx for instant execution without compilation. Here's how to get started:

### 1. Get Google Gemini API Key

1. Visit [Google AI Studio](https://aistudio.google.com/)
2. Sign in with your Google account
3. Click "Get API Key"
4. Create a new API key
5. Copy the key

### 2. Configure Environment

Edit the `.env` file and replace the API key:

```env
GEMINI_API_KEY=your_actual_api_key_here
```

### 3. Run Trial Mode (Recommended)

Test with 1-3 pages first:

```bash
pnpm trial
```

This will process the first 3 pages of `./pdf/list.pdf` and create an Excel file in `./output/`.

### 4. Process Full PDFs

Once trial mode works, process larger documents:

```bash
# Process full PDF
pnpm dev process ./pdf/list.pdf

# Process specific pages
pnpm dev process ./pdf/list.pdf --start-page 1 --end-page 10 --trial

# Process different PDF
pnpm dev process ./pdf/others/GH\ 1_1-150.pdf --trial
```

## 📁 Available PDF Files

Your workspace contains several PDF files ready for processing:

- `./pdf/list.pdf` (220MB) - Main directory listing
- `./pdf/others/A-Z-GH 1-Full.pdf` (255MB) - Complete A-Z guide
- `./pdf/others/GH 1_1-150.pdf` (124MB) - Pages 1-150
- `./pdf/others/GH 2_151-250.pdf` (83MB) - Pages 151-250
- `./pdf/others/GH 3_251-324.pdf` (48MB) - Pages 251-324

## 🔧 Configuration Options

Modify `.env` file for different settings:

```env
# Processing settings
TRIAL_MAX_PAGES=3          # Pages to process in trial mode
MAX_PAGES_PER_BATCH=25     # Pages per batch in full mode
IMAGE_DPI=300              # Image quality (72-600)
IMAGE_QUALITY=95           # JPEG quality (1-100)

# Output settings
OUTPUT_DIR=./output        # Where Excel files are saved
INCLUDE_TIMESTAMP=true     # Add timestamp to filenames
```

## 📊 Expected Output

The application will create Excel files with:

### Main Data Sheet
- **Business Name** - Extracted company/organization names
- **Category** - Breeder, Propagator, Nursery, etc.
- **Contact Person** - Individual contact names
- **Email** - Email addresses found
- **Phone** - Phone numbers (all formats)
- **Website** - URLs and web addresses
- **Address** - Physical addresses
- **Social Media** - Social media handles/URLs
- **Brand Names** - Product brands mentioned
- **Comments** - Additional information
- **Page Number** - Source page reference
- **Confidence %** - AI extraction confidence

### Metadata Sheet
- Processing statistics
- Categories found
- Contact information summary
- Configuration used

## 🎨 Excel Formatting

- **Green rows**: High confidence (80%+)
- **Yellow rows**: Medium confidence (60-79%)
- **Red rows**: Low confidence (<60%)
- **Auto-filter**: Enabled for all columns
- **Frozen header**: First row stays visible

## ⚡ Performance Tips

### For Large PDFs:
1. Start with trial mode to test quality
2. Use smaller batch sizes if memory issues occur
3. Process in sections using `--start-page` and `--end-page`
4. Monitor the `./logs/app.log` file for detailed progress

### For Best OCR Results:
- Use high DPI (300+) for small text
- Ensure PDF pages are properly oriented
- Check that text is clear and not blurred

## 🐛 Troubleshooting

### Common Issues:

**"API Key Error"**
- Verify your Gemini API key is correct
- Check if you have API quota remaining
- Ensure the key has Gemini API access

**"PDF Conversion Failed"**
- Check if PDF is password-protected
- Verify file is not corrupted
- Try with a smaller PDF first

**"No Data Extracted"**
- Check if PDF contains actual text (not just images)
- Try adjusting IMAGE_DPI setting
- Review the log file for detailed errors

**"Memory Issues"**
- Reduce MAX_PAGES_PER_BATCH to 10 or less
- Process PDFs in smaller sections
- Close other applications to free memory

### Debug Mode:

Set detailed logging in `.env`:
```env
LOG_LEVEL=debug
```

Then check `./logs/app.log` for detailed information.

## 📈 Cost Estimation

Google Gemini API pricing (approximate):
- Trial mode (3 pages): ~$0.01-0.05
- 25 pages: ~$0.10-0.25
- 100 pages: ~$0.50-1.00

*Actual costs depend on image size and complexity*

## 🔄 Iterative Processing

This is designed for iterative development:

1. **Start small**: Use trial mode with 1-3 pages
2. **Validate quality**: Check extraction accuracy
3. **Adjust settings**: Modify DPI, batch size as needed
4. **Scale up**: Process larger sections
5. **Refine prompts**: Modify Gemini prompts for better results

## 📞 Next Steps

1. **Test trial mode** with your API key
2. **Review sample output** to understand data quality
3. **Adjust extraction prompts** in `src/services/geminiService.ts` if needed
4. **Scale to full processing** once satisfied with results
5. **Customize Excel output** format if required

## 🎉 You're Ready!

Run this to verify everything is working:

```bash
node test-setup.js
```

Then start with:

```bash
npm run trial
```

Good luck with your OCR processing! 🚀
