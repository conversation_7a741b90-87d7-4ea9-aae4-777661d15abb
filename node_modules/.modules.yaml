hoistPattern:
  - '*'
hoistedDependencies:
  '@colors/colors@1.6.0':
    '@colors/colors': private
  '@dabh/diagnostics@2.0.3':
    '@dabh/diagnostics': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.28.0)':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/config-array@0.20.0':
    '@eslint/config-array': public
  '@eslint/config-helpers@0.2.2':
    '@eslint/config-helpers': public
  '@eslint/core@0.14.0':
    '@eslint/core': public
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': public
  '@eslint/js@9.28.0':
    '@eslint/js': public
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.3.1':
    '@eslint/plugin-kit': public
  '@fast-csv/format@4.3.5':
    '@fast-csv/format': private
  '@fast-csv/parse@4.3.6':
    '@fast-csv/parse': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@img/sharp-darwin-arm64@0.34.2':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.34.2':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.1.0':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.1.0':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.1.0':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.1.0':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-ppc64@1.1.0':
    '@img/sharp-libvips-linux-ppc64': private
  '@img/sharp-libvips-linux-s390x@1.1.0':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.1.0':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.34.2':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.34.2':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.34.2':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.34.2':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.34.2':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.34.2':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.34.2':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-arm64@0.34.2':
    '@img/sharp-win32-arm64': private
  '@img/sharp-win32-ia32@0.34.2':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.34.2':
    '@img/sharp-win32-x64': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgr/core@0.2.7':
    '@pkgr/core': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/triple-beam@1.3.5':
    '@types/triple-beam': private
  '@typescript-eslint/project-service@8.34.0(typescript@5.8.3)':
    '@typescript-eslint/project-service': public
  '@typescript-eslint/scope-manager@8.34.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/tsconfig-utils@8.34.0(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': public
  '@typescript-eslint/type-utils@8.34.0(eslint@9.28.0)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@8.34.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@8.34.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@8.34.0(eslint@9.28.0)(typescript@5.8.3)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@8.34.0':
    '@typescript-eslint/visitor-keys': public
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-styles@4.3.0:
    ansi-styles: private
  archiver-utils@2.1.0:
    archiver-utils: private
  archiver@5.3.2:
    archiver: private
  argparse@2.0.1:
    argparse: private
  array-parallel@0.1.3:
    array-parallel: private
  array-series@0.1.5:
    array-series: private
  async@3.2.6:
    async: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  big-integer@1.6.52:
    big-integer: private
  binary@0.3.0:
    binary: private
  bl@4.1.0:
    bl: private
  bluebird@3.4.7:
    bluebird: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-indexof-polyfill@1.0.2:
    buffer-indexof-polyfill: private
  buffer@5.7.1:
    buffer: private
  buffers@0.1.1:
    buffers: private
  callsites@3.1.0:
    callsites: private
  chainsaw@0.1.0:
    chainsaw: private
  chalk@4.1.2:
    chalk: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  colorspace@1.1.4:
    colorspace: private
  compress-commons@4.1.2:
    compress-commons: private
  concat-map@0.0.1:
    concat-map: private
  core-util-is@1.0.3:
    core-util-is: private
  crc-32@1.2.2:
    crc-32: private
  crc32-stream@4.0.3:
    crc32-stream: private
  cross-spawn@7.0.6:
    cross-spawn: private
  dayjs@1.11.13:
    dayjs: private
  debug@4.4.1:
    debug: private
  deep-is@0.1.4:
    deep-is: private
  detect-libc@2.0.4:
    detect-libc: private
  duplexer2@0.1.4:
    duplexer2: private
  enabled@2.0.0:
    enabled: private
  end-of-stream@1.4.4:
    end-of-stream: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.4.0:
    eslint-scope: public
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: public
  espree@10.4.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  fast-csv@4.3.6:
    fast-csv: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fecha@4.2.3:
    fecha: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  fn.name@1.1.0:
    fn.name: private
  fs-constants@1.0.0:
    fs-constants: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fstream@1.0.12:
    fstream: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@7.2.3:
    glob: private
  globals@14.0.0:
    globals: private
  gm@1.25.1:
    gm: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  ieee754@1.2.1:
    ieee754: private
  ignore@7.0.5:
    ignore: private
  immediate@3.0.6:
    immediate: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-stream@2.0.1:
    is-stream: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  jszip@3.10.1:
    jszip: private
  keyv@4.5.4:
    keyv: private
  kuler@2.0.0:
    kuler: private
  lazystream@1.0.1:
    lazystream: private
  levn@0.4.1:
    levn: private
  lie@3.3.0:
    lie: private
  listenercount@1.0.1:
    listenercount: private
  locate-path@6.0.0:
    locate-path: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.difference@4.5.0:
    lodash.difference: private
  lodash.escaperegexp@4.1.2:
    lodash.escaperegexp: private
  lodash.flatten@4.4.0:
    lodash.flatten: private
  lodash.groupby@4.6.0:
    lodash.groupby: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isequal@4.5.0:
    lodash.isequal: private
  lodash.isfunction@3.0.9:
    lodash.isfunction: private
  lodash.isnil@4.0.0:
    lodash.isnil: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isundefined@3.0.1:
    lodash.isundefined: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.union@4.6.0:
    lodash.union: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  logform@2.7.0:
    logform: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  mkdirp@0.5.6:
    mkdirp: private
  ms@2.1.3:
    ms: private
  natural-compare@1.4.0:
    natural-compare: private
  normalize-path@3.0.0:
    normalize-path: private
  once@1.4.0:
    once: private
  one-time@1.0.0:
    one-time: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  pako@1.0.11:
    pako: private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  picomatch@2.3.1:
    picomatch: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: public
  process-nextick-args@2.0.1:
    process-nextick-args: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  readable-stream@3.6.2:
    readable-stream: private
  readdir-glob@1.1.3:
    readdir-glob: private
  resolve-from@4.0.0:
    resolve-from: private
  reusify@1.1.0:
    reusify: private
  rimraf@2.7.1:
    rimraf: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-buffer@5.1.2:
    safe-buffer: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  saxes@5.0.1:
    saxes: private
  semver@7.7.2:
    semver: private
  setimmediate@1.0.5:
    setimmediate: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  stack-trace@0.0.10:
    stack-trace: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  supports-color@7.2.0:
    supports-color: private
  synckit@0.11.8:
    synckit: private
  tar-stream@2.2.0:
    tar-stream: private
  text-hex@1.0.0:
    text-hex: private
  tmp@0.2.3:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  traverse@0.3.9:
    traverse: private
  triple-beam@1.4.1:
    triple-beam: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  type-check@0.4.0:
    type-check: private
  undici-types@7.8.0:
    undici-types: private
  unzipper@0.10.14:
    unzipper: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@8.3.2:
    uuid: private
  which@2.0.2:
    which: private
  winston-transport@4.9.0:
    winston-transport: private
  word-wrap@1.2.5:
    word-wrap: private
  wrappy@1.0.2:
    wrappy: private
  xmlchars@2.2.0:
    xmlchars: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zip-stream@4.1.1:
    zip-stream: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.0.0
pendingBuilds: []
prunedAt: Wed, 11 Jun 2025 19:24:01 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/runtime@1.4.3'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-darwin-x64@0.34.2'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.1.0'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.1.0'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-arm@1.1.0'
  - '@img/sharp-libvips-linux-ppc64@1.1.0'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-s390x@1.1.0'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.1.0'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm64@0.34.2'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-arm@0.34.2'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-s390x@0.34.2'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linux-x64@0.34.2'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.34.2'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.34.2'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-wasm32@0.34.2'
  - '@img/sharp-win32-arm64@0.34.2'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@img/sharp-win32-ia32@0.34.2'
  - '@img/sharp-win32-x64@0.33.5'
  - '@img/sharp-win32-x64@0.34.2'
  - tslib@2.8.1
storeDir: /Users/<USER>/Library/pnpm/store/v3
virtualStoreDir: .pnpm
