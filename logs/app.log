{"level":"info","message":"============================================================","service":"guide-ocr","timestamp":"2025-06-11T19:38:23.652Z"}
{"level":"info","message":"GuideOCR - PDF Processing Started","service":"guide-ocr","timestamp":"2025-06-11T19:38:23.654Z"}
{"level":"info","message":"============================================================","service":"guide-ocr","timestamp":"2025-06-11T19:38:23.654Z"}
{"level":"info","message":"PDF File: ./pdf/list.pdf","service":"guide-ocr","timestamp":"2025-06-11T19:38:23.654Z"}
{"level":"info","message":"Trial Mode: true","service":"guide-ocr","timestamp":"2025-06-11T19:38:23.654Z"}
{"level":"info","message":"Batch Size: 3","service":"guide-ocr","timestamp":"2025-06-11T19:38:23.654Z"}
{"level":"info","message":"Step 1: Converting PDF to images...","service":"guide-ocr","timestamp":"2025-06-11T19:38:23.654Z"}
{"level":"error","message":"PDF validation failed for ./pdf/list.pdf: File too large: 230865453 bytes (max: 104857600 bytes)","service":"guide-ocr","stack":"Error: File too large: 230865453 bytes (max: 104857600 bytes)\n    at Function.validatePdfFile (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/utils/fileUtils.ts:88:15)\n    at async PdfProcessor.convertPdfToImages (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/services/pdfProcessor.ts:25:7)\n    at async GuideOCR.processPdf (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:47:32)\n    at async Command.<anonymous> (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:184:24)\n    at async Command.parseAsync (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/node_modules/.pnpm/commander@14.0.0/node_modules/commander/lib/command.js:1123:5)\n    at async main (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:200:3)","timestamp":"2025-06-11T19:38:23.657Z"}
{"level":"error","message":"PDF conversion failed after 3ms: File too large: 230865453 bytes (max: 104857600 bytes)","service":"guide-ocr","stack":"Error: File too large: 230865453 bytes (max: 104857600 bytes)\n    at Function.validatePdfFile (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/utils/fileUtils.ts:88:15)\n    at async PdfProcessor.convertPdfToImages (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/services/pdfProcessor.ts:25:7)\n    at async GuideOCR.processPdf (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:47:32)\n    at async Command.<anonymous> (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:184:24)\n    at async Command.parseAsync (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/node_modules/.pnpm/commander@14.0.0/node_modules/commander/lib/command.js:1123:5)\n    at async main (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:200:3)","timestamp":"2025-06-11T19:38:23.657Z"}
{"level":"error","message":"Processing failed after 6ms: PDF conversion failed: File too large: 230865453 bytes (max: 104857600 bytes)","service":"guide-ocr","stack":"Error: PDF conversion failed: File too large: 230865453 bytes (max: 104857600 bytes)\n    at GuideOCR.processPdf (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:50:15)\n    at async Command.<anonymous> (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:184:24)\n    at async Command.parseAsync (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/node_modules/.pnpm/commander@14.0.0/node_modules/commander/lib/command.js:1123:5)\n    at async main (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:200:3)","timestamp":"2025-06-11T19:38:23.658Z"}
{"level":"info","message":"Cleaned up 0 temporary files","service":"guide-ocr","timestamp":"2025-06-11T19:38:23.659Z"}
{"level":"info","message":"============================================================","service":"guide-ocr","timestamp":"2025-06-11T19:42:28.994Z"}
{"level":"info","message":"GuideOCR - PDF Processing Started","service":"guide-ocr","timestamp":"2025-06-11T19:42:28.997Z"}
{"level":"info","message":"============================================================","service":"guide-ocr","timestamp":"2025-06-11T19:42:28.997Z"}
{"level":"info","message":"PDF File: ./pdf/list.pdf","service":"guide-ocr","timestamp":"2025-06-11T19:42:28.997Z"}
{"level":"info","message":"Trial Mode: true","service":"guide-ocr","timestamp":"2025-06-11T19:42:28.997Z"}
{"level":"info","message":"Batch Size: 3","service":"guide-ocr","timestamp":"2025-06-11T19:42:28.997Z"}
{"level":"info","message":"Step 1: Converting PDF to images...","service":"guide-ocr","timestamp":"2025-06-11T19:42:28.998Z"}
{"level":"error","message":"PDF validation failed for ./pdf/list.pdf: File too large: 230865453 bytes (max: 104857600 bytes)","service":"guide-ocr","stack":"Error: File too large: 230865453 bytes (max: 104857600 bytes)\n    at Function.validatePdfFile (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/utils/fileUtils.ts:88:15)\n    at async PdfProcessor.convertPdfToImages (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/services/pdfProcessor.ts:26:7)\n    at async GuideOCR.processPdf (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:43:32)\n    at async Command.<anonymous> (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:176:24)\n    at async Command.parseAsync (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/node_modules/.pnpm/commander@14.0.0/node_modules/commander/lib/command.js:1123:5)\n    at async main (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:193:3)","timestamp":"2025-06-11T19:42:29.000Z"}
{"level":"error","message":"PDF conversion failed after 2ms: File too large: 230865453 bytes (max: 104857600 bytes)","service":"guide-ocr","stack":"Error: File too large: 230865453 bytes (max: 104857600 bytes)\n    at Function.validatePdfFile (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/utils/fileUtils.ts:88:15)\n    at async PdfProcessor.convertPdfToImages (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/services/pdfProcessor.ts:26:7)\n    at async GuideOCR.processPdf (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:43:32)\n    at async Command.<anonymous> (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:176:24)\n    at async Command.parseAsync (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/node_modules/.pnpm/commander@14.0.0/node_modules/commander/lib/command.js:1123:5)\n    at async main (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:193:3)","timestamp":"2025-06-11T19:42:29.000Z"}
{"level":"error","message":"Processing failed after 7ms: PDF conversion failed: File too large: 230865453 bytes (max: 104857600 bytes)","service":"guide-ocr","stack":"Error: PDF conversion failed: File too large: 230865453 bytes (max: 104857600 bytes)\n    at GuideOCR.processPdf (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:46:15)\n    at async Command.<anonymous> (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:176:24)\n    at async Command.parseAsync (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/node_modules/.pnpm/commander@14.0.0/node_modules/commander/lib/command.js:1123:5)\n    at async main (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:193:3)","timestamp":"2025-06-11T19:42:29.001Z"}
{"level":"info","message":"Cleaned up 0 temporary files","service":"guide-ocr","timestamp":"2025-06-11T19:42:29.001Z"}
{"level":"info","message":"============================================================","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.751Z"}
{"level":"info","message":"GuideOCR - PDF Processing Started","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.754Z"}
{"level":"info","message":"============================================================","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.754Z"}
{"level":"info","message":"PDF File: ./pdf/list.pdf","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.754Z"}
{"level":"info","message":"Trial Mode: true","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.754Z"}
{"level":"info","message":"Batch Size: 3","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.755Z"}
{"level":"info","message":"Step 1: Converting PDF to images...","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.755Z"}
{"level":"info","message":"Converting PDF to images: ./pdf/list.pdf","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.756Z"}
{"level":"info","message":"Page range: 1 to 3","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.756Z"}
{"level":"info","message":"Max pages: 3","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.756Z"}
{"level":"warn","message":"Failed to convert page 1: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[0]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.1.png\" this most likely means the gm/convert binaries can't be found","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.765Z"}
{"level":"warn","message":"Failed to convert page 2: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[1]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.2.png\" this most likely means the gm/convert binaries can't be found","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.767Z"}
{"level":"warn","message":"Failed to convert page 3: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[2]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.3.png\" this most likely means the gm/convert binaries can't be found","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.769Z"}
{"level":"info","message":"Successfully converted 0 pages","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.769Z"}
{"level":"info","message":"PDF conversion completed in 14ms","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.769Z"}
{"level":"error","message":"Processing failed after 18ms: PDF conversion failed: Failed to convert page 1: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[0]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.1.png\" this most likely means the gm/convert binaries can't be found, Failed to convert page 2: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[1]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.2.png\" this most likely means the gm/convert binaries can't be found, Failed to convert page 3: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[2]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.3.png\" this most likely means the gm/convert binaries can't be found","service":"guide-ocr","stack":"Error: PDF conversion failed: Failed to convert page 1: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[0]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.1.png\" this most likely means the gm/convert binaries can't be found, Failed to convert page 2: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[1]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.2.png\" this most likely means the gm/convert binaries can't be found, Failed to convert page 3: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[2]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.3.png\" this most likely means the gm/convert binaries can't be found\n    at GuideOCR.processPdf (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:46:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Command.<anonymous> (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:176:24)\n    at async Command.parseAsync (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/node_modules/.pnpm/commander@14.0.0/node_modules/commander/lib/command.js:1123:5)\n    at async main (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:193:3)","timestamp":"2025-06-11T19:43:37.770Z"}
{"level":"info","message":"Cleaned up 0 temporary files","service":"guide-ocr","timestamp":"2025-06-11T19:43:37.772Z"}
{"level":"info","message":"============================================================","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.204Z"}
{"level":"info","message":"GuideOCR - PDF Processing Started","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.206Z"}
{"level":"info","message":"============================================================","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.207Z"}
{"level":"info","message":"PDF File: ./pdf/list.pdf","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.207Z"}
{"level":"info","message":"Trial Mode: true","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.207Z"}
{"level":"info","message":"Batch Size: 3","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.207Z"}
{"level":"info","message":"Step 1: Converting PDF to images...","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.207Z"}
{"level":"info","message":"Converting PDF to images: ./pdf/list.pdf","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.209Z"}
{"level":"info","message":"Page range: 1 to 3","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.209Z"}
{"level":"info","message":"Max pages: 3","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.209Z"}
{"level":"warn","message":"Failed to convert page 1: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[0]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.1.png\" this most likely means the gm/convert binaries can't be found","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.212Z"}
{"level":"warn","message":"Failed to convert page 2: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[1]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.2.png\" this most likely means the gm/convert binaries can't be found","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.214Z"}
{"level":"warn","message":"Failed to convert page 3: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[2]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.3.png\" this most likely means the gm/convert binaries can't be found","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.216Z"}
{"level":"info","message":"Successfully converted 0 pages","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.216Z"}
{"level":"info","message":"PDF conversion completed in 9ms","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.216Z"}
{"level":"error","message":"Processing failed after 12ms: PDF conversion failed: Failed to convert page 1: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[0]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.1.png\" this most likely means the gm/convert binaries can't be found, Failed to convert page 2: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[1]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.2.png\" this most likely means the gm/convert binaries can't be found, Failed to convert page 3: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[2]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.3.png\" this most likely means the gm/convert binaries can't be found","service":"guide-ocr","stack":"Error: PDF conversion failed: Failed to convert page 1: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[0]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.1.png\" this most likely means the gm/convert binaries can't be found, Failed to convert page 2: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[1]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.2.png\" this most likely means the gm/convert binaries can't be found, Failed to convert page 3: Error: Could not execute GraphicsMagick/ImageMagick: gm \"convert\" \"-density\" \"300x300\" \"-quality\" \"95\" \"-[2]\" \"-units\" \"PixelsPerInch\" \"-resize\" \"2048x2048!\" \"-compress\" \"jpeg\" \"./temp/page.3.png\" this most likely means the gm/convert binaries can't be found\n    at GuideOCR.processPdf (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:46:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Command.<anonymous> (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:176:24)\n    at async Command.parseAsync (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/node_modules/.pnpm/commander@14.0.0/node_modules/commander/lib/command.js:1123:5)\n    at async main (/Users/<USER>/Dropbox/Code/Projects/GuideOCR/src/index.ts:193:3)","timestamp":"2025-06-11T19:44:17.217Z"}
{"level":"info","message":"Cleaned up 0 temporary files","service":"guide-ocr","timestamp":"2025-06-11T19:44:17.218Z"}
