#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

console.log('🧪 GuideOCR Setup Test');
console.log('='.repeat(50));

// Check if build directory exists
const distDir = './dist';
if (fs.existsSync(distDir)) {
  console.log('✅ Build directory exists');

  // Check if main files are built
  const mainFile = path.join(distDir, 'index.js');
  if (fs.existsSync(mainFile)) {
    console.log('✅ Main application built successfully');
  } else {
    console.log('❌ Main application not found');
  }
} else {
  console.log('❌ Build directory not found');
}

// Check PDF files
const pdfDir = './pdf';
if (fs.existsSync(pdfDir)) {
  const pdfFiles = fs.readdirSync(pdfDir).filter(file => file.endsWith('.pdf'));
  console.log(`✅ Found ${pdfFiles.length} PDF files in ./pdf/`);
  pdfFiles.forEach(file => {
    const filePath = path.join(pdfDir, file);
    const stats = fs.statSync(filePath);
    const sizeMB = (stats.size / (1024 * 1024)).toFixed(2);
    console.log(`   📄 ${file} (${sizeMB} MB)`);
  });

  // Check others directory
  const othersDir = path.join(pdfDir, 'others');
  if (fs.existsSync(othersDir)) {
    const otherPdfs = fs.readdirSync(othersDir).filter(file => file.endsWith('.pdf'));
    console.log(`✅ Found ${otherPdfs.length} additional PDF files in ./pdf/others/`);
    otherPdfs.forEach(file => {
      const filePath = path.join(othersDir, file);
      const stats = fs.statSync(filePath);
      const sizeMB = (stats.size / (1024 * 1024)).toFixed(2);
      console.log(`   📄 ${file} (${sizeMB} MB)`);
    });
  }
} else {
  console.log('❌ PDF directory not found');
}

// Check environment file
const envFile = './.env';
if (fs.existsSync(envFile)) {
  console.log('✅ Environment file exists');
  const envContent = fs.readFileSync(envFile, 'utf8');
  if (envContent.includes('GEMINI_API_KEY=your_gemini_api_key_here')) {
    console.log('⚠️  Please update your GEMINI_API_KEY in .env file');
  } else if (envContent.includes('GEMINI_API_KEY=')) {
    console.log('✅ GEMINI_API_KEY is configured');
  }
} else {
  console.log('❌ Environment file not found');
}

// Check required directories
const requiredDirs = ['./output', './temp', './logs'];
requiredDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`✅ Created directory: ${dir}`);
  } else {
    console.log(`✅ Directory exists: ${dir}`);
  }
});

console.log('='.repeat(50));
console.log('🎯 Next Steps:');
console.log('1. Get your Google Gemini API key from: https://aistudio.google.com/');
console.log('2. Update GEMINI_API_KEY in .env file');
console.log('3. Run trial mode: pnpm trial');
console.log('4. Or process a specific PDF: pnpm dev process ./pdf/list.pdf --trial');
console.log('='.repeat(50));
