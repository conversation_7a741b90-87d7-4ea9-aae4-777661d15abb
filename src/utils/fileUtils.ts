import fs from 'fs';
import path from 'path';
import { logger } from './logger';

export class FileUtils {
  /**
   * Check if a file exists and is readable
   */
  static async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.promises.access(filePath, fs.constants.F_OK | fs.constants.R_OK);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Ensure directory exists, create if it doesn't
   */
  static async ensureDirectory(dirPath: string): Promise<void> {
    try {
      await fs.promises.mkdir(dirPath, { recursive: true });
    } catch (error) {
      logger.error(`Failed to create directory ${dirPath}:`, error);
      throw error;
    }
  }

  /**
   * Clean up temporary files
   */
  static async cleanupTempFiles(tempDir: string): Promise<void> {
    try {
      if (await this.fileExists(tempDir)) {
        const files = await fs.promises.readdir(tempDir);
        await Promise.all(
          files.map(file =>
            fs.promises
              .unlink(path.join(tempDir, file))
              .catch(err => logger.warn(`Failed to delete temp file ${file}:`, err))
          )
        );
        logger.info(`Cleaned up ${files.length} temporary files`);
      }
    } catch (error) {
      logger.error('Failed to cleanup temp files:', error);
    }
  }

  /**
   * Get file size in bytes
   */
  static async getFileSize(filePath: string): Promise<number> {
    try {
      const stats = await fs.promises.stat(filePath);
      return stats.size;
    } catch (error) {
      logger.error(`Failed to get file size for ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Get file extension
   */
  static getFileExtension(filePath: string): string {
    return path.extname(filePath).toLowerCase();
  }

  /**
   * Validate PDF file
   */
  static async validatePdfFile(filePath: string): Promise<boolean> {
    try {
      if (!(await this.fileExists(filePath))) {
        throw new Error(`File does not exist: ${filePath}`);
      }

      const extension = this.getFileExtension(filePath);
      if (extension !== '.pdf') {
        throw new Error(`File is not a PDF: ${filePath}`);
      }

      const fileSize = await this.getFileSize(filePath);
      const maxSize = 100 * 1024 * 1024; // 100MB limit
      if (fileSize > maxSize) {
        throw new Error(`File too large: ${fileSize} bytes (max: ${maxSize} bytes)`);
      }

      return true;
    } catch (error) {
      logger.error(`PDF validation failed for ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Generate unique filename with timestamp
   */
  static generateUniqueFilename(baseName: string, extension: string): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return `${baseName}_${timestamp}${extension}`;
  }

  /**
   * Copy file to destination
   */
  static async copyFile(source: string, destination: string): Promise<void> {
    try {
      await this.ensureDirectory(path.dirname(destination));
      await fs.promises.copyFile(source, destination);
      logger.debug(`File copied from ${source} to ${destination}`);
    } catch (error) {
      logger.error(`Failed to copy file from ${source} to ${destination}:`, error);
      throw error;
    }
  }

  /**
   * Read file as buffer
   */
  static async readFileAsBuffer(filePath: string): Promise<Buffer> {
    try {
      return await fs.promises.readFile(filePath);
    } catch (error) {
      logger.error(`Failed to read file ${filePath}:`, error);
      throw error;
    }
  }
}
