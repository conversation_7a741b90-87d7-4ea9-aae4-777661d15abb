import winston from 'winston';
import { Config } from '../config/config';

export class Logger {
  private static instance: winston.Logger;

  public static getInstance(): winston.Logger {
    if (!Logger.instance) {
      const config = Config.getInstance();

      Logger.instance = winston.createLogger({
        level: config.logLevel,
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.errors({ stack: true }),
          winston.format.json()
        ),
        defaultMeta: { service: 'guide-ocr' },
        transports: [
          new winston.transports.File({
            filename: config.logFile,
            maxsize: 5242880, // 5MB
            maxFiles: 5
          }),
          new winston.transports.Console({
            format: winston.format.combine(winston.format.colorize(), winston.format.simple())
          })
        ]
      });
    }

    return Logger.instance;
  }
}

export const logger = Logger.getInstance();
