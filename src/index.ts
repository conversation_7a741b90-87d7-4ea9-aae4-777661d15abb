#!/usr/bin/env tsx

import { Command } from 'commander';
import { Config } from './config/config';
import { logger } from './utils/logger';
import { PdfProcessor } from './services/pdfProcessor';
import { GeminiService } from './services/geminiService';
import { ExcelExporter } from './services/excelExporter';
import { ProcessingResult, BatchProcessingOptions } from './types/types';

class GuideOCR {
  private config: Config;
  private pdfProcessor: PdfProcessor;
  private geminiService: GeminiService;
  private excelExporter: ExcelExporter;

  constructor() {
    this.config = Config.getInstance();
    this.pdfProcessor = new PdfProcessor();
    this.geminiService = new GeminiService();
    this.excelExporter = new ExcelExporter();
  }

  /**
   * Main processing function
   */
  async processPdf(pdfPath: string, options: BatchProcessingOptions): Promise<ProcessingResult> {
    const startTime = Date.now();

    try {
      logger.info('='.repeat(60));
      logger.info('GuideOCR - PDF Processing Started');
      logger.info('='.repeat(60));
      logger.info(`PDF File: ${pdfPath}`);
      logger.info(`Trial Mode: ${options.trialMode}`);
      logger.info(`Batch Size: ${options.batchSize}`);

      // Ensure directories exist
      this.config.ensureDirectories();

      // Step 1: Convert PDF to images
      logger.info('Step 1: Converting PDF to images...');
      const conversionResult = await this.pdfProcessor.convertPdfToImages(pdfPath, options);

      if (!conversionResult.success || conversionResult.imagePaths.length === 0) {
        throw new Error(`PDF conversion failed: ${conversionResult.errors.join(', ')}`);
      }

      logger.info(`Successfully converted ${conversionResult.imagePaths.length} pages to images`);

      // Step 2: Process images with Gemini
      logger.info('Step 2: Processing images with Gemini AI...');
      const geminiResult = await this.geminiService.processImages(conversionResult.imagePaths);

      if (geminiResult.extractedData.length === 0) {
        logger.warn('No data extracted from images');
      }

      logger.info(`Extracted ${geminiResult.extractedData.length} business entries`);

      // Step 3: Export to Excel
      logger.info('Step 3: Exporting to Excel...');
      const processingTime = Date.now() - startTime;
      const stats = this.excelExporter.generateStats(geminiResult.extractedData, processingTime);

      const excelPath = await this.excelExporter.exportToExcel(geminiResult.extractedData, stats, {
        includeTimestamp: true
      });

      // Step 4: Cleanup
      logger.info('Step 4: Cleaning up temporary files...');
      await this.pdfProcessor.cleanup();

      // Final results
      const finalProcessingTime = Date.now() - startTime;
      logger.info('='.repeat(60));
      logger.info('Processing Completed Successfully!');
      logger.info(`Total Processing Time: ${finalProcessingTime}ms`);
      logger.info(`Excel File: ${excelPath}`);
      logger.info(`Entries Extracted: ${geminiResult.extractedData.length}`);
      logger.info(`Average Confidence: ${geminiResult.confidence.toFixed(1)}%`);
      logger.info('='.repeat(60));

      return {
        success: true,
        data: geminiResult.extractedData,
        errors: [...conversionResult.errors, ...geminiResult.processingNotes],
        totalPages: conversionResult.totalPages,
        processedPages: conversionResult.imagePaths.length,
        processingTime: finalProcessingTime
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error(`Processing failed after ${processingTime}ms:`, error);

      // Cleanup on error
      try {
        await this.pdfProcessor.cleanup();
      } catch (cleanupError) {
        logger.error('Cleanup failed:', cleanupError);
      }

      return {
        success: false,
        data: [],
        errors: [error instanceof Error ? error.message : String(error)],
        totalPages: 0,
        processedPages: 0,
        processingTime
      };
    }
  }
}

// CLI Setup
async function main() {
  const program = new Command();

  program
    .name('guide-ocr')
    .description('OCR processing of PDF files with Google Gemini AI')
    .version('1.0.0');

  program
    .command('process')
    .description('Process a PDF file')
    .argument('<pdf-path>', 'Path to the PDF file')
    .option('-t, --trial', 'Run in trial mode (1-3 pages only)')
    .option('-s, --start-page <number>', 'Start page number', '1')
    .option('-e, --end-page <number>', 'End page number (-1 for all)', '-1')
    .option('-b, --batch-size <number>', 'Batch size for processing', '25')
    .action(async (pdfPath: string, options: any) => {
      try {
        const guideOCR = new GuideOCR();

        const processingOptions: BatchProcessingOptions = {
          startPage: parseInt(options.startPage),
          endPage: parseInt(options.endPage),
          batchSize: parseInt(options.batchSize),
          trialMode: options.trial || false
        };

        const result = await guideOCR.processPdf(pdfPath, processingOptions);

        if (result.success) {
          console.log('\n✅ Processing completed successfully!');
          console.log(`📊 Extracted ${result.data.length} entries`);
          console.log(`⏱️  Processing time: ${result.processingTime}ms`);
        } else {
          console.error('\n❌ Processing failed:');
          result.errors.forEach(error => console.error(`   ${error}`));
          process.exit(1);
        }
      } catch (error) {
        console.error('❌ Application error:', error);
        process.exit(1);
      }
    });

  program
    .command('trial')
    .description('Run in trial mode with default PDF')
    .argument('[pdf-path]', 'Path to the PDF file', './pdf/list.pdf')
    .action(async (pdfPath: string) => {
      try {
        const guideOCR = new GuideOCR();

        const processingOptions: BatchProcessingOptions = {
          startPage: 1,
          endPage: 3,
          batchSize: 3,
          trialMode: true
        };

        console.log('🧪 Running in trial mode...');
        const result = await guideOCR.processPdf(pdfPath, processingOptions);

        if (result.success) {
          console.log('\n✅ Trial completed successfully!');
          console.log(
            `📊 Extracted ${result.data.length} entries from ${result.processedPages} pages`
          );
        } else {
          console.error('\n❌ Trial failed:');
          result.errors.forEach(error => console.error(`   ${error}`));
        }
      } catch (error) {
        console.error('❌ Trial error:', error);
        process.exit(1);
      }
    });

  await program.parseAsync();
}

// Run the application
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { GuideOCR };
