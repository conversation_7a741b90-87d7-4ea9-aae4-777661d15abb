import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { ProcessingConfig } from '../types/types';

// Load environment variables
dotenv.config();

export class Config {
  private static instance: Config;

  public readonly geminiApiKey: string;
  public readonly processingConfig: ProcessingConfig;
  public readonly logLevel: string;
  public readonly logFile: string;

  private constructor() {
    // Validate required environment variables
    this.geminiApiKey = this.getRequiredEnvVar('GEMINI_API_KEY');

    // Processing configuration
    this.processingConfig = {
      trialMode: process.env.TRIAL_MODE === 'true',
      maxPagesPerBatch: parseInt(process.env.MAX_PAGES_PER_BATCH || '25'),
      trialMaxPages: parseInt(process.env.TRIAL_MAX_PAGES || '3'),
      outputDir: process.env.OUTPUT_DIR || './output',
      tempDir: process.env.TEMP_DIR || './temp',
      imageFormat: (process.env.IMAGE_FORMAT as 'png' | 'tiff' | 'jpeg') || 'png',
      imageQuality: parseInt(process.env.IMAGE_QUALITY || '95'),
      imageDPI: parseInt(process.env.IMAGE_DPI || '300')
    };

    // Logging configuration
    this.logLevel = process.env.LOG_LEVEL || 'info';
    this.logFile = process.env.LOG_FILE || './logs/app.log';

    // Validate configuration
    this.validateConfig();
  }

  public static getInstance(): Config {
    if (!Config.instance) {
      Config.instance = new Config();
    }
    return Config.instance;
  }

  private getRequiredEnvVar(name: string): string {
    const value = process.env[name];
    if (!value) {
      throw new Error(`Required environment variable ${name} is not set`);
    }
    return value;
  }

  private validateConfig(): void {
    const { processingConfig } = this;

    if (processingConfig.maxPagesPerBatch < 1 || processingConfig.maxPagesPerBatch > 50) {
      throw new Error('MAX_PAGES_PER_BATCH must be between 1 and 50');
    }

    if (processingConfig.trialMaxPages < 1 || processingConfig.trialMaxPages > 10) {
      throw new Error('TRIAL_MAX_PAGES must be between 1 and 10');
    }

    if (processingConfig.imageQuality < 1 || processingConfig.imageQuality > 100) {
      throw new Error('IMAGE_QUALITY must be between 1 and 100');
    }

    if (processingConfig.imageDPI < 72 || processingConfig.imageDPI > 600) {
      throw new Error('IMAGE_DPI must be between 72 and 600');
    }
  }

  public getExcelFilename(): string {
    const baseFilename = process.env.EXCEL_FILENAME || 'extracted_data.xlsx';
    const includeTimestamp = process.env.INCLUDE_TIMESTAMP === 'true';

    if (includeTimestamp) {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const ext = path.extname(baseFilename);
      const name = path.basename(baseFilename, ext);
      return `${name}_${timestamp}${ext}`;
    }

    return baseFilename;
  }

  public ensureDirectories(): void {
    const dirs = [
      this.processingConfig.outputDir,
      this.processingConfig.tempDir,
      path.dirname(this.logFile)
    ];

    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }
}
