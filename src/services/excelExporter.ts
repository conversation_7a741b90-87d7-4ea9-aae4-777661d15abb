import ExcelJS from 'exceljs';
import path from 'path';
import { Config } from '../config/config';
import { logger } from '../utils/logger';
import { FileUtils } from '../utils/fileUtils';
import { ExtractedData, ProcessingStats, ExcelExportOptions } from '../types/types';

export class ExcelExporter {
  private config: Config;

  constructor() {
    this.config = Config.getInstance();
  }

  /**
   * Export extracted data to Excel file
   */
  async exportToExcel(
    data: ExtractedData[],
    stats: ProcessingStats,
    options?: Partial<ExcelExportOptions>
  ): Promise<string> {
    const startTime = Date.now();

    try {
      // Prepare export options
      const exportOptions: ExcelExportOptions = {
        filename: options?.filename || this.config.getExcelFilename(),
        includeTimestamp: options?.includeTimestamp ?? true,
        sheetName: options?.sheetName || 'Extracted Data',
        includeMetadata: options?.includeMetadata ?? true
      };

      // Ensure output directory exists
      await FileUtils.ensureDirectory(this.config.processingConfig.outputDir);

      // Create workbook
      const workbook = new ExcelJS.Workbook();

      // Set workbook properties
      workbook.creator = 'GuideOCR';
      workbook.lastModifiedBy = 'GuideOCR';
      workbook.created = new Date();
      workbook.modified = new Date();

      // Create main data worksheet
      const worksheet = workbook.addWorksheet(exportOptions.sheetName);

      // Setup columns
      this.setupWorksheetColumns(worksheet);

      // Add data rows
      this.addDataRows(worksheet, data);

      // Apply formatting
      this.applyFormatting(worksheet, data.length);

      // Add metadata sheet if requested
      if (exportOptions.includeMetadata) {
        this.addMetadataSheet(workbook, stats, data.length);
      }

      // Generate file path
      const filePath = path.join(this.config.processingConfig.outputDir, exportOptions.filename);

      // Write file
      await workbook.xlsx.writeFile(filePath);

      const processingTime = Date.now() - startTime;
      logger.info(`Excel export completed in ${processingTime}ms: ${filePath}`);

      return filePath;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error(`Excel export failed after ${processingTime}ms:`, error);
      throw error;
    }
  }

  /**
   * Setup worksheet columns
   */
  private setupWorksheetColumns(worksheet: ExcelJS.Worksheet): void {
    worksheet.columns = [
      { header: 'Business Name', key: 'businessName', width: 30 },
      { header: 'Category', key: 'category', width: 20 },
      { header: 'Contact Person', key: 'contactPerson', width: 25 },
      { header: 'Email', key: 'email', width: 35 },
      { header: 'Phone', key: 'phone', width: 20 },
      { header: 'Website', key: 'website', width: 40 },
      { header: 'Address', key: 'address', width: 50 },
      { header: 'Social Media', key: 'socialMedia', width: 40 },
      { header: 'Brand Names', key: 'brandNames', width: 30 },
      { header: 'Comments', key: 'comments', width: 60 },
      { header: 'Page Number', key: 'pageNumber', width: 15 },
      { header: 'Confidence %', key: 'confidence', width: 15 }
    ];
  }

  /**
   * Add data rows to worksheet
   */
  private addDataRows(worksheet: ExcelJS.Worksheet, data: ExtractedData[]): void {
    data.forEach((item, _index) => {
      const row = worksheet.addRow({
        businessName: item.businessName,
        category: item.category,
        contactPerson: item.contactPerson || '',
        email: item.email || '',
        phone: item.phone || '',
        website: item.website || '',
        address: item.address || '',
        socialMedia: item.socialMedia?.join('; ') || '',
        brandNames: item.brandNames?.join('; ') || '',
        comments: item.comments,
        pageNumber: item.pageNumber,
        confidence: Math.round(item.confidence)
      });

      // Color code based on confidence
      if (item.confidence < 60) {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFFFCCCC' } // Light red
        };
      } else if (item.confidence < 80) {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFFFFFCC' } // Light yellow
        };
      } else {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFCCFFCC' } // Light green
        };
      }
    });
  }

  /**
   * Apply formatting to worksheet
   */
  private applyFormatting(worksheet: ExcelJS.Worksheet, dataRowCount: number): void {
    // Header formatting
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF366092' }
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };
    headerRow.height = 25;

    // Auto-filter
    worksheet.autoFilter = {
      from: 'A1',
      to: `L${dataRowCount + 1}`
    };

    // Freeze header row
    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    // Add borders
    worksheet.eachRow((row, _rowNumber) => {
      row.eachCell(cell => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
    });

    // Wrap text for comments column
    const commentsColumn = worksheet.getColumn('comments');
    commentsColumn.alignment = { wrapText: true, vertical: 'top' };
  }

  /**
   * Add metadata sheet with processing statistics
   */
  private addMetadataSheet(
    workbook: ExcelJS.Workbook,
    stats: ProcessingStats,
    totalExtracted: number
  ): void {
    const metaSheet = workbook.addWorksheet('Processing Metadata');

    // Processing summary
    metaSheet.addRow(['Processing Summary']);
    metaSheet.addRow(['Total Entries Extracted', totalExtracted]);
    metaSheet.addRow(['Processing Time (ms)', stats.processingTimeMs]);
    metaSheet.addRow(['Processing Date', new Date().toISOString()]);
    metaSheet.addRow([]);

    // Categories found
    metaSheet.addRow(['Categories Found']);
    stats.categoriesFound.forEach(category => {
      metaSheet.addRow(['', category]);
    });
    metaSheet.addRow([]);

    // Contact information summary
    metaSheet.addRow(['Contact Information Summary']);
    metaSheet.addRow(['Emails Extracted', stats.emailsExtracted]);
    metaSheet.addRow(['Phones Extracted', stats.phonesExtracted]);
    metaSheet.addRow(['Websites Extracted', stats.websitesExtracted]);
    metaSheet.addRow(['Social Media Extracted', stats.socialMediaExtracted]);
    metaSheet.addRow([]);

    // Configuration used
    metaSheet.addRow(['Configuration Used']);
    metaSheet.addRow(['Trial Mode', this.config.processingConfig.trialMode]);
    metaSheet.addRow(['Max Pages Per Batch', this.config.processingConfig.maxPagesPerBatch]);
    metaSheet.addRow(['Image Format', this.config.processingConfig.imageFormat]);
    metaSheet.addRow(['Image DPI', this.config.processingConfig.imageDPI]);

    // Format metadata sheet
    metaSheet.getColumn(1).width = 25;
    metaSheet.getColumn(2).width = 30;

    // Bold headers
    [1, 6, 11, 17].forEach(rowNum => {
      const row = metaSheet.getRow(rowNum);
      row.font = { bold: true };
    });
  }

  /**
   * Generate processing statistics
   */
  generateStats(data: ExtractedData[], processingTimeMs: number): ProcessingStats {
    const categories = [...new Set(data.map(item => item.category))];

    return {
      totalEntries: data.length,
      categoriesFound: categories,
      emailsExtracted: data.filter(item => item.email).length,
      phonesExtracted: data.filter(item => item.phone).length,
      websitesExtracted: data.filter(item => item.website).length,
      socialMediaExtracted: data.filter(item => item.socialMedia && item.socialMedia.length > 0)
        .length,
      processingTimeMs
    };
  }
}
