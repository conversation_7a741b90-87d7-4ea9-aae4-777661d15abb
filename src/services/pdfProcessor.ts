import pdf2pic from 'pdf2pic';
import { Config } from '../config/config';
import { FileUtils } from '../utils/fileUtils';
import { logger } from '../utils/logger';
import { ImageConversionResult, BatchProcessingOptions } from '../types/types';
import { Convert } from 'pdf2pic/dist/types/convert';

export class PdfProcessor {
  private config: Config;

  constructor() {
    this.config = Config.getInstance();
  }

  /**
   * Convert PDF to images
   */
  async convertPdfToImages(
    pdfPath: string,
    options?: BatchProcessingOptions
  ): Promise<ImageConversionResult> {
    const startTime = Date.now();

    try {
      // Validate PDF file
      await FileUtils.validatePdfFile(pdfPath);

      // Ensure temp directory exists
      await FileUtils.ensureDirectory(this.config.processingConfig.tempDir);

      // Configure pdf2pic
      const convert = pdf2pic.fromPath(pdfPath, {
        density: this.config.processingConfig.imageDPI,
        saveFilename: 'page',
        savePath: this.config.processingConfig.tempDir,
        format: this.config.processingConfig.imageFormat,
        width: 2048,
        height: 2048,
        quality: this.config.processingConfig.imageQuality
      });

      // Determine page range
      const startPage = options?.startPage || 1;
      const endPage = options?.endPage || -1; // -1 means all pages
      const maxPages = options?.trialMode
        ? this.config.processingConfig.trialMaxPages
        : this.config.processingConfig.maxPagesPerBatch;

      logger.info(`Converting PDF to images: ${pdfPath}`);
      logger.info(`Page range: ${startPage} to ${endPage === -1 ? 'end' : endPage}`);
      logger.info(`Max pages: ${maxPages}`);

      // Convert pages
      const results = await this.convertPages(convert, startPage, endPage, maxPages);

      const processingTime = Date.now() - startTime;
      logger.info(`PDF conversion completed in ${processingTime}ms`);

      return {
        success: true,
        imagePaths: results.imagePaths,
        totalPages: results.totalPages,
        errors: results.errors
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error(`PDF conversion failed after ${processingTime}ms:`, error);

      return {
        success: false,
        imagePaths: [],
        totalPages: 0,
        errors: [error instanceof Error ? error.message : String(error)]
      };
    }
  }

  /**
   * Convert specific pages to images
   */
  private async convertPages(
    convert: Convert,
    startPage: number,
    endPage: number,
    maxPages: number
  ): Promise<{ imagePaths: string[]; totalPages: number; errors: string[] }> {
    const imagePaths: string[] = [];
    const errors: string[] = [];
    let totalPages = 0;

    try {
      // Convert pages in batches
      let currentPage = startPage;
      let pagesProcessed = 0;

      while (pagesProcessed < maxPages && (endPage === -1 || currentPage <= endPage)) {
        try {
          const result = await convert(currentPage, { responseType: 'image' });

          if (result?.path) {
            imagePaths.push(result.path);
            logger.debug(`Converted page ${currentPage}: ${result.path}`);
          } else {
            // No more pages
            break;
          }

          currentPage++;
          pagesProcessed++;
          totalPages++;
        } catch (pageError) {
          const errorMsg = `Failed to convert page ${currentPage}: ${pageError}`;
          logger.warn(errorMsg);
          errors.push(errorMsg);

          // Try next page
          currentPage++;

          // If we get too many consecutive errors, stop
          if (errors.length > 5) {
            logger.error('Too many conversion errors, stopping');
            break;
          }
        }
      }

      logger.info(`Successfully converted ${imagePaths.length} pages`);

      return { imagePaths, totalPages, errors };
    } catch (error) {
      logger.error('Batch conversion failed:', error);
      throw error;
    }
  }

  /**
   * Get PDF page count (estimate)
   */
  async getPdfPageCount(pdfPath: string): Promise<number> {
    try {
      // This is a simple estimation method
      // For more accurate page counting, you might want to use a different library
      const convert = pdf2pic.fromPath(pdfPath, {
        density: 72, // Low density for quick check
        format: 'png'
      });

      let pageCount = 0;
      let currentPage = 1;

      // Try to convert pages until we fail
      while (currentPage <= 1000) {
        // Safety limit
        try {
          const result = await convert(currentPage, { responseType: 'base64' });
          if (result) {
            pageCount = currentPage;
            currentPage++;
          } else {
            break;
          }
        } catch {
          break;
        }
      }

      logger.info(`Estimated PDF page count: ${pageCount}`);
      return pageCount;
    } catch (error) {
      logger.error('Failed to get PDF page count:', error);
      return 0;
    }
  }

  /**
   * Clean up converted images
   */
  async cleanup(): Promise<void> {
    await FileUtils.cleanupTempFiles(this.config.processingConfig.tempDir);
  }
}
