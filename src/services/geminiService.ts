import { GenerativeModel, GoogleGenerativeAI, InlineDataPart } from '@google/generative-ai';
import fs from 'fs';
import { Config } from '../config/config';
import { logger } from '../utils/logger';
import { GeminiResponse, ExtractedData } from '../types/types';

// Interface for raw JSON data from Gemini response
interface RawGeminiData {
  businessName?: string;
  category?: string;
  contactPerson?: string;
  email?: string;
  phone?: string;
  website?: string;
  address?: string;
  socialMedia?: string[];
  brandNames?: string[];
  comments?: string;
  pageNumber?: number;
  confidence?: number;
}

export class GeminiService {
  private genAI: GoogleGenerativeAI;
  private model: GenerativeModel;
  private config: Config;

  constructor() {
    this.config = Config.getInstance();
    this.genAI = new GoogleGenerativeAI(this.config.geminiApiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.0-flash-exp' });
  }

  /**
   * Process images with Gemini Vision API
   */
  async processImages(imagePaths: string[]): Promise<GeminiResponse> {
    const startTime = Date.now();

    try {
      logger.info(`Processing ${imagePaths.length} images with Gemini`);

      // Prepare images for API
      const imageParts = await this.prepareImageParts(imagePaths);

      // Create the prompt for directory listing extraction
      const prompt = this.createExtractionPrompt();

      // Call Gemini API
      const result = await this.model.generateContent([prompt, ...imageParts]);
      const response = await result.response;
      const text = response.text();

      logger.debug('Gemini raw response:', text);

      // Parse the response
      const parsedData = this.parseGeminiResponse(text, imagePaths.length);

      const processingTime = Date.now() - startTime;
      logger.info(`Gemini processing completed in ${processingTime}ms`);

      return parsedData;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error(`Gemini processing failed after ${processingTime}ms:`, error);

      return {
        extractedData: [],
        confidence: 0,
        processingNotes: [`Error: ${error instanceof Error ? error.message : String(error)}`]
      };
    }
  }

  /**
   * Prepare image parts for Gemini API
   */
  private async prepareImageParts(imagePaths: string[]): Promise<InlineDataPart[]> {
    const imageParts = [];

    for (const imagePath of imagePaths) {
      try {
        const imageBuffer = await fs.promises.readFile(imagePath);
        const base64Data = imageBuffer.toString('base64');

        imageParts.push({
          inlineData: {
            data: base64Data,
            mimeType: this.getMimeType(imagePath)
          }
        });

        logger.debug(`Prepared image: ${imagePath}`);
      } catch (error) {
        logger.error(`Failed to prepare image ${imagePath}:`, error);
      }
    }

    return imageParts;
  }

  /**
   * Get MIME type based on file extension
   */
  private getMimeType(filePath: string): string {
    const ext = filePath.toLowerCase().split('.').pop();
    switch (ext) {
      case 'png':
        return 'image/png';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'tiff':
      case 'tif':
        return 'image/tiff';
      default:
        return 'image/png';
    }
  }

  /**
   * Create extraction prompt for directory listings
   */
  private createExtractionPrompt(): string {
    return `
You are an expert OCR and data extraction specialist. Analyze these directory listing pages and extract business information in a structured format.

TASK: Extract all business entries from these directory pages. The pages contain listings for categories like Breeders, Propagators, Nurseries, etc.

EXTRACTION REQUIREMENTS:
1. Extract each business/organization entry separately
2. For each entry, identify and extract:
   - Business/Organization name
   - Category (Breeder, Propagator, Nursery, etc.)
   - Contact person name (if available)
   - Email addresses
   - Phone numbers (including international formats)
   - Website URLs
   - Physical addresses
   - Social media handles/URLs (Facebook, Instagram, Twitter, etc.)
   - Brand names or product lines mentioned
   - Any other relevant business information

3. Handle various formats:
   - Text may be in columns, flowing format, or mixed layouts
   - Some entries may have icons for social media
   - Some may have logos or brand imagery
   - Phone numbers may be in various international formats
   - Addresses may be abbreviated or full format

4. Data quality:
   - Extract complete information even if partially visible
   - If text is unclear, note it in comments
   - Maintain original formatting for addresses
   - Preserve all contact methods found

RESPONSE FORMAT:
Return a JSON array of business entries. Each entry should have this structure:
{
  "businessName": "string",
  "category": "string",
  "contactPerson": "string or null",
  "email": "string or null",
  "phone": "string or null",
  "website": "string or null",
  "address": "string or null",
  "socialMedia": ["array of social media URLs/handles"],
  "brandNames": ["array of brand names mentioned"],
  "comments": "string - any additional info that doesn't fit other fields",
  "pageNumber": number,
  "confidence": number (0-100)
}

IMPORTANT:
- Extract ALL entries visible in the images
- If multiple contact methods exist, include all of them
- Use "comments" field for any information that doesn't fit standard fields
- Be thorough - don't miss entries due to formatting differences
- Assign confidence scores based on text clarity and completeness

Begin extraction:`;
  }

  /**
   * Parse Gemini response into structured data
   */
  private parseGeminiResponse(responseText: string, pageCount: number): GeminiResponse {
    try {
      // Try to extract JSON from the response
      const jsonMatch = responseText.match(/\[[\s\S]*\]/);

      if (!jsonMatch) {
        logger.warn('No JSON array found in Gemini response');
        return {
          extractedData: [],
          confidence: 0,
          processingNotes: ['Failed to parse JSON from response']
        };
      }

      const jsonData = JSON.parse(jsonMatch[0]);

      if (!Array.isArray(jsonData)) {
        logger.warn('Parsed data is not an array');
        return {
          extractedData: [],
          confidence: 0,
          processingNotes: ['Response is not a valid array']
        };
      }

      // Convert to ExtractedData format
      const extractedData: ExtractedData[] = jsonData.map((item: RawGeminiData, index: number) => {
        const result: ExtractedData = {
          category: item.category || 'Unknown',
          businessName: item.businessName || `Entry ${index + 1}`,
          comments: item.comments || '',
          pageNumber: item.pageNumber || 1,
          confidence: typeof item.confidence === 'number' ? item.confidence : 80
        };

        // Only add optional properties if they have values
        if (item.contactPerson) result.contactPerson = item.contactPerson;
        if (item.email) result.email = item.email;
        if (item.phone) result.phone = item.phone;
        if (item.website) result.website = item.website;
        if (item.address) result.address = item.address;
        if (Array.isArray(item.socialMedia) && item.socialMedia.length > 0) {
          result.socialMedia = item.socialMedia;
        }
        if (Array.isArray(item.brandNames) && item.brandNames.length > 0) {
          result.brandNames = item.brandNames;
        }

        return result;
      });

      // Calculate overall confidence
      const avgConfidence =
        extractedData.length > 0
          ? extractedData.reduce((sum, item) => sum + item.confidence, 0) / extractedData.length
          : 0;

      logger.info(
        `Extracted ${extractedData.length} entries with average confidence ${avgConfidence.toFixed(1)}%`
      );

      return {
        extractedData,
        confidence: avgConfidence,
        processingNotes: [
          `Successfully extracted ${extractedData.length} entries from ${pageCount} pages`
        ]
      };
    } catch (error) {
      logger.error('Failed to parse Gemini response:', error);
      logger.debug('Raw response:', responseText);

      return {
        extractedData: [],
        confidence: 0,
        processingNotes: [`Parse error: ${error instanceof Error ? error.message : String(error)}`]
      };
    }
  }
}
